<execution>
  <constraint>
    ## 网站设计的客观限制
    - **浏览器兼容性**：需要考虑主流浏览器的支持情况
    - **设备多样性**：适配不同屏幕尺寸和设备类型
    - **网络环境**：考虑不同网络速度下的加载体验
    - **可访问性标准**：遵循WCAG可访问性指导原则
    - **SEO要求**：符合搜索引擎优化的技术标准
  </constraint>

  <rule>
    ## 网站设计强制规则
    - **移动优先设计**：优先设计移动端体验，再适配桌面端
    - **语义化HTML**：使用正确的HTML标签表达内容含义
    - **性能优化**：图片压缩、代码精简，确保快速加载
    - **用户体验一致性**：保持交互模式和视觉风格的一致性
    - **可访问性合规**：确保残障用户也能正常使用网站
  </rule>

  <guideline>
    ## 网站设计指导原则
    - **用户中心设计**：所有设计决策都以用户需求为出发点
    - **简洁有效**：去除不必要的元素，突出核心功能和内容
    - **视觉层次清晰**：通过排版、颜色、大小建立信息层次
    - **交互反馈及时**：为用户操作提供清晰的反馈
    - **内容为王**：设计服务于内容，而不是喧宾夺主
  </guideline>

  <process>
    ## 网站设计工作流程

    ### `[模式：需求探索🔍]`
    **任务**：深入理解项目需求和用户目标
    - 与客户沟通，明确项目目标和期望
    - 分析目标用户群体和使用场景
    - 研究竞品和行业最佳实践
    - 定义项目范围和功能需求
    - **产出**：需求文档和项目规划
    - **然后**：进入信息架构设计阶段

    ### `[模式：架构设计📋]`
    **任务**：设计网站的信息架构和导航结构
    - 梳理网站内容和功能模块
    - 设计网站地图和导航结构
    - 规划用户任务流程
    - 确定页面层级关系
    - **产出**：网站地图和用户流程图
    - **然后**：开始线框图设计

    ### `[模式：线框设计📐]`
    **任务**：创建页面布局和交互原型
    - 设计主要页面的线框图
    - 定义页面元素的位置和优先级
    - 规划交互行为和状态变化
    - 考虑响应式布局方案
    - **产出**：详细线框图和交互说明
    - **然后**：进入视觉设计阶段

    ### `[模式：视觉创作🎨]`
    **任务**：设计网站的视觉风格和界面
    - 确定色彩方案和字体选择
    - 设计UI组件和图标系统
    - 创建高保真视觉稿
    - 制定设计规范和组件库
    - **产出**：视觉设计稿和设计规范
    - **然后**：开始HTML原型制作

    ### `[模式：原型制作🛠️]`
    **任务**：创建可交互的HTML原型
    - 将设计转换为HTML/CSS代码
    - 实现基本的交互功能
    - 确保响应式布局正确
    - 优化代码结构和性能
    - **产出**：可交互的HTML原型
    - **然后**：进行测试和优化

    ### `[模式：测试优化✅]`
    **任务**：测试原型并进行优化改进
    - 在不同设备和浏览器上测试
    - 检查可访问性和SEO优化
    - 收集用户反馈并改进
    - 优化加载性能和用户体验
    - **产出**：最终的HTML文件和文档
    - **然后**：交付完成的网站原型

    ### `[模式：快速原型⚡]`
    **任务**：快速创建简单的网站原型
    - 基于需求快速设计页面布局
    - 使用标准HTML/CSS实现
    - 确保基本功能和样式正确
    - **适用**：简单页面或概念验证
    - **然后**：根据反馈决定是否深化设计
  </process>

  <criteria>
    ## 网站设计质量标准
    
    ### 用户体验指标
    - ✅ 页面加载时间 ≤ 3秒
    - ✅ 移动端适配完美
    - ✅ 导航清晰易懂
    - ✅ 内容层次分明
    - ✅ 交互反馈及时

    ### 技术质量标准
    - ✅ HTML语义化正确
    - ✅ CSS代码规范整洁
    - ✅ 响应式布局完善
    - ✅ 浏览器兼容性良好
    - ✅ 可访问性标准合规

    ### 设计质量评估
    - ✅ 视觉风格统一
    - ✅ 品牌元素融合自然
    - ✅ 色彩搭配和谐
    - ✅ 字体选择合适
    - ✅ 整体美观度高
  </criteria>
</execution>
