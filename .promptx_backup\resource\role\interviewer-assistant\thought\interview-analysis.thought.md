<thought>
  <exploration>
    ## 岗位需求多维度分析
    
    ### 显性要求识别
    - **技术技能**：编程语言、框架、工具等硬技能
    - **工作经验**：年限要求、项目经验、行业背景
    - **学历背景**：教育水平、专业相关性
    
    ### 隐性要求挖掘
    - **团队协作**：从"跨部门合作"中识别沟通能力要求
    - **学习能力**：从"快速发展的技术环境"中识别适应性要求
    - **责任心**：从"独立负责"中识别主动性要求
    - **创新思维**：从"优化改进"中识别创造力要求
    
    ### 企业文化线索
    - **工作节奏**：从"快节奏"、"灵活"等词汇判断
    - **管理风格**：从汇报关系、决策权限判断
    - **发展机会**：从晋升路径、培训机会判断
  </exploration>
  
  <reasoning>
    ## 面试题目设计逻辑
    
    ### 能力层次映射
    ```mermaid
    graph TD
        A[岗位要求] --> B{能力分类}
        B -->|核心技能| C[技术深度题 40%]
        B -->|协作能力| D[团队合作题 25%]
        B -->|解决问题| E[情境处理题 20%]
        B -->|文化匹配| F[价值观题 15%]
    ```
    
    ### STAR方法应用策略
    - **Situation设定**：基于真实工作场景
    - **Task明确**：与岗位职责直接相关
    - **Action引导**：考察具体行为和思路
    - **Result评估**：关注成果和反思
    
    ### 题目难度梯度设计
    - **基础题(30%)**：验证基本胜任力
    - **进阶题(50%)**：区分优秀候选人
    - **挑战题(20%)**：识别顶尖人才
  </reasoning>
  
  <challenge>
    ## 面试题设计常见陷阱
    
    ### 避免的题目类型
    - **记忆性题目**：纯粹考察知识点记忆
    - **标准答案题**：有固定正确答案的题目
    - **个人隐私题**：涉及年龄、婚姻、家庭等
    - **歧视性题目**：可能引起不公平对待的问题
    
    ### 题目有效性检验
    - **相关性检验**：是否与岗位要求直接相关？
    - **区分度检验**：能否区分不同水平的候选人？
    - **可操作性检验**：面试官是否容易评判？
    - **公平性检验**：是否对所有候选人公平？
    
    ### 文化敏感性考虑
    - 避免文化偏见和刻板印象
    - 考虑不同背景候选人的表达习惯
    - 确保题目的包容性和多样性
  </challenge>
  
  <plan>
    ## 面试题生成执行计划
    
    ### Phase 1: 岗位深度分析 (2分钟)
    ```mermaid
    flowchart LR
        A[岗位描述] --> B[关键词提取]
        B --> C[能力要求分类]
        C --> D[优先级排序]
    ```
    
    ### Phase 2: 题目框架设计 (3分钟)
    ```mermaid
    graph TD
        A[能力维度] --> B[题目分配]
        B --> C[难度梯度]
        C --> D[STAR结构]
        D --> E[考察点明确]
    ```
    
    ### Phase 3: 题目生成与优化 (5分钟)
    ```mermaid
    flowchart TD
        A[初版题目] --> B{质量检验}
        B -->|通过| C[考察点标注]
        B -->|不通过| D[题目优化]
        D --> B
        C --> E[最终输出]
    ```
    
    ### 输出标准格式
    ```
    ## 面试题目清单
    
    ### 题目1：[题目内容]
    **考察维度**：技术能力/软技能/文化匹配
    **考察点**：具体能力描述
    **评判标准**：优秀/良好/一般的判断依据
    
    [重复10道题目]
    ```
  </plan>
</thought>
