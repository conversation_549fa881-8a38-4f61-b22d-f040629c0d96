{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-08-07T14:11:15.459Z", "updatedAt": "2025-08-07T14:11:15.484Z", "resourceCount": 29}, "resources": [{"id": "workspace-structure", "source": "project", "protocol": "knowledge", "name": "Workspace Structure 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/knowledge/workspace-structure.knowledge.md", "metadata": {"createdAt": "2025-08-07T14:11:15.462Z", "updatedAt": "2025-08-07T14:11:15.462Z", "scannedAt": "2025-08-07T14:11:15.462Z", "path": "knowledge/workspace-structure.knowledge.md"}}, {"id": "da<PERSON><PERSON>", "source": "project", "protocol": "role", "name": "Daxiong 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/daxiong/daxiong.role.md", "metadata": {"createdAt": "2025-08-07T14:11:15.463Z", "updatedAt": "2025-08-07T14:11:15.463Z", "scannedAt": "2025-08-07T14:11:15.463Z", "path": "role/daxiong/daxiong.role.md"}}, {"id": "anti-ai-detection", "source": "project", "protocol": "execution", "name": "Anti Ai Detection 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/daxiong/execution/anti-ai-detection.execution.md", "metadata": {"createdAt": "2025-08-07T14:11:15.464Z", "updatedAt": "2025-08-07T14:11:15.464Z", "scannedAt": "2025-08-07T14:11:15.464Z", "path": "role/daxiong/execution/anti-ai-detection.execution.md"}}, {"id": "daxiong-workflow", "source": "project", "protocol": "execution", "name": "Daxiong Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/daxiong/execution/daxiong-workflow.execution.md", "metadata": {"createdAt": "2025-08-07T14:11:15.465Z", "updatedAt": "2025-08-07T14:11:15.465Z", "scannedAt": "2025-08-07T14:11:15.465Z", "path": "role/daxiong/execution/daxiong-workflow.execution.md"}}, {"id": "enhanced-capabilities", "source": "project", "protocol": "execution", "name": "Enhanced Capabilities 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/daxiong/execution/enhanced-capabilities.execution.md", "metadata": {"createdAt": "2025-08-07T14:11:15.466Z", "updatedAt": "2025-08-07T14:11:15.466Z", "scannedAt": "2025-08-07T14:11:15.466Z", "path": "role/daxiong/execution/enhanced-capabilities.execution.md"}}, {"id": "a-stock-expertise", "source": "project", "protocol": "knowledge", "name": "A Stock Expertise 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/role/daxiong/knowledge/a-stock-expertise.knowledge.md", "metadata": {"createdAt": "2025-08-07T14:11:15.467Z", "updatedAt": "2025-08-07T14:11:15.467Z", "scannedAt": "2025-08-07T14:11:15.467Z", "path": "role/daxiong/knowledge/a-stock-expertise.knowledge.md"}}, {"id": "content-creation", "source": "project", "protocol": "knowledge", "name": "Content Creation 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/role/daxiong/knowledge/content-creation.knowledge.md", "metadata": {"createdAt": "2025-08-07T14:11:15.467Z", "updatedAt": "2025-08-07T14:11:15.467Z", "scannedAt": "2025-08-07T14:11:15.467Z", "path": "role/daxiong/knowledge/content-creation.knowledge.md"}}, {"id": "daxiong-mindset", "source": "project", "protocol": "thought", "name": "Daxiong Mindset 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/daxiong/thought/daxiong-mindset.thought.md", "metadata": {"createdAt": "2025-08-07T14:11:15.468Z", "updatedAt": "2025-08-07T14:11:15.468Z", "scannedAt": "2025-08-07T14:11:15.468Z", "path": "role/daxiong/thought/daxiong-mindset.thought.md"}}, {"id": "interview-question-generation", "source": "project", "protocol": "execution", "name": "Interview Question Generation 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/interviewer-assistant/execution/interview-question-generation.execution.md", "metadata": {"createdAt": "2025-08-07T14:11:15.469Z", "updatedAt": "2025-08-07T14:11:15.469Z", "scannedAt": "2025-08-07T14:11:15.469Z", "path": "role/interviewer-assistant/execution/interview-question-generation.execution.md"}}, {"id": "interviewer-assistant", "source": "project", "protocol": "role", "name": "Interviewer Assistant 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/interviewer-assistant/interviewer-assistant.role.md", "metadata": {"createdAt": "2025-08-07T14:11:15.470Z", "updatedAt": "2025-08-07T14:11:15.470Z", "scannedAt": "2025-08-07T14:11:15.470Z", "path": "role/interviewer-assistant/interviewer-assistant.role.md"}}, {"id": "interview-analysis", "source": "project", "protocol": "thought", "name": "Interview Analysis 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/interviewer-assistant/thought/interview-analysis.thought.md", "metadata": {"createdAt": "2025-08-07T14:11:15.471Z", "updatedAt": "2025-08-07T14:11:15.471Z", "scannedAt": "2025-08-07T14:11:15.471Z", "path": "role/interviewer-assistant/thought/interview-analysis.thought.md"}}, {"id": "python-cat-workflow", "source": "project", "protocol": "execution", "name": "Python Cat Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/python-cat-girl/execution/python-cat-workflow.execution.md", "metadata": {"createdAt": "2025-08-07T14:11:15.471Z", "updatedAt": "2025-08-07T14:11:15.471Z", "scannedAt": "2025-08-07T14:11:15.471Z", "path": "role/python-cat-girl/execution/python-cat-workflow.execution.md"}}, {"id": "python-standards", "source": "project", "protocol": "execution", "name": "Python Standards 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/python-cat-girl/execution/python-standards.execution.md", "metadata": {"createdAt": "2025-08-07T14:11:15.472Z", "updatedAt": "2025-08-07T14:11:15.472Z", "scannedAt": "2025-08-07T14:11:15.472Z", "path": "role/python-cat-girl/execution/python-standards.execution.md"}}, {"id": "python-expertise", "source": "project", "protocol": "knowledge", "name": "Python Expertise 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/role/python-cat-girl/knowledge/python-expertise.knowledge.md", "metadata": {"createdAt": "2025-08-07T14:11:15.473Z", "updatedAt": "2025-08-07T14:11:15.473Z", "scannedAt": "2025-08-07T14:11:15.473Z", "path": "role/python-cat-girl/knowledge/python-expertise.knowledge.md"}}, {"id": "python-cat-girl", "source": "project", "protocol": "role", "name": "Python Cat Girl 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/python-cat-girl/python-cat-girl.role.md", "metadata": {"createdAt": "2025-08-07T14:11:15.473Z", "updatedAt": "2025-08-07T14:11:15.473Z", "scannedAt": "2025-08-07T14:11:15.473Z", "path": "role/python-cat-girl/python-cat-girl.role.md"}}, {"id": "python-cat-thinking", "source": "project", "protocol": "thought", "name": "Python Cat Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/python-cat-girl/thought/python-cat-thinking.thought.md", "metadata": {"createdAt": "2025-08-07T14:11:15.474Z", "updatedAt": "2025-08-07T14:11:15.474Z", "scannedAt": "2025-08-07T14:11:15.474Z", "path": "role/python-cat-girl/thought/python-cat-thinking.thought.md"}}, {"id": "html-standards", "source": "project", "protocol": "execution", "name": "Html Standards 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/web-expert/execution/html-standards.execution.md", "metadata": {"createdAt": "2025-08-07T14:11:15.476Z", "updatedAt": "2025-08-07T14:11:15.476Z", "scannedAt": "2025-08-07T14:11:15.476Z", "path": "role/web-expert/execution/html-standards.execution.md"}}, {"id": "web-design-workflow", "source": "project", "protocol": "execution", "name": "Web Design Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/web-expert/execution/web-design-workflow.execution.md", "metadata": {"createdAt": "2025-08-07T14:11:15.476Z", "updatedAt": "2025-08-07T14:11:15.476Z", "scannedAt": "2025-08-07T14:11:15.476Z", "path": "role/web-expert/execution/web-design-workflow.execution.md"}}, {"id": "web-expertise", "source": "project", "protocol": "knowledge", "name": "Web Expertise 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/role/web-expert/knowledge/web-expertise.knowledge.md", "metadata": {"createdAt": "2025-08-07T14:11:15.477Z", "updatedAt": "2025-08-07T14:11:15.477Z", "scannedAt": "2025-08-07T14:11:15.477Z", "path": "role/web-expert/knowledge/web-expertise.knowledge.md"}}, {"id": "web-design-thinking", "source": "project", "protocol": "thought", "name": "Web Design Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/web-expert/thought/web-design-thinking.thought.md", "metadata": {"createdAt": "2025-08-07T14:11:15.478Z", "updatedAt": "2025-08-07T14:11:15.478Z", "scannedAt": "2025-08-07T14:11:15.478Z", "path": "role/web-expert/thought/web-design-thinking.thought.md"}}, {"id": "web-expert", "source": "project", "protocol": "role", "name": "Web Expert 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/web-expert/web-expert.role.md", "metadata": {"createdAt": "2025-08-07T14:11:15.478Z", "updatedAt": "2025-08-07T14:11:15.478Z", "scannedAt": "2025-08-07T14:11:15.478Z", "path": "role/web-expert/web-expert.role.md"}}, {"id": "autonovels-core-prompts", "source": "project", "protocol": "execution", "name": "Autonovels Core Prompts 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/xiongbao/execution/autonovels-core-prompts.execution.md", "metadata": {"createdAt": "2025-08-07T14:11:15.479Z", "updatedAt": "2025-08-07T14:11:15.479Z", "scannedAt": "2025-08-07T14:11:15.479Z", "path": "role/xiongbao/execution/autonovels-core-prompts.execution.md"}}, {"id": "novel-writing-workflow", "source": "project", "protocol": "execution", "name": "Novel Writing Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/xiongbao/execution/novel-writing-workflow.execution.md", "metadata": {"createdAt": "2025-08-07T14:11:15.480Z", "updatedAt": "2025-08-07T14:11:15.480Z", "scannedAt": "2025-08-07T14:11:15.480Z", "path": "role/xiongbao/execution/novel-writing-workflow.execution.md"}}, {"id": "workspace-management", "source": "project", "protocol": "execution", "name": "Workspace Management 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/xiongbao/execution/workspace-management.execution.md", "metadata": {"createdAt": "2025-08-07T14:11:15.481Z", "updatedAt": "2025-08-07T14:11:15.481Z", "scannedAt": "2025-08-07T14:11:15.481Z", "path": "role/xiongbao/execution/workspace-management.execution.md"}}, {"id": "autonovels-system", "source": "project", "protocol": "knowledge", "name": "Autonovels System 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/role/xiongbao/knowledge/autonovels-system.knowledge.md", "metadata": {"createdAt": "2025-08-07T14:11:15.482Z", "updatedAt": "2025-08-07T14:11:15.482Z", "scannedAt": "2025-08-07T14:11:15.482Z", "path": "role/xiongbao/knowledge/autonovels-system.knowledge.md"}}, {"id": "xiongbao-workspace", "source": "project", "protocol": "knowledge", "name": "Xiongbao Workspace 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/role/xiongbao/knowledge/xiongbao-workspace.knowledge.md", "metadata": {"createdAt": "2025-08-07T14:11:15.482Z", "updatedAt": "2025-08-07T14:11:15.482Z", "scannedAt": "2025-08-07T14:11:15.482Z", "path": "role/xiongbao/knowledge/xiongbao-workspace.knowledge.md"}}, {"id": "autonovels-creation-thinking", "source": "project", "protocol": "thought", "name": "Autonovels Creation Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/xiongbao/thought/autonovels-creation-thinking.thought.md", "metadata": {"createdAt": "2025-08-07T14:11:15.483Z", "updatedAt": "2025-08-07T14:11:15.483Z", "scannedAt": "2025-08-07T14:11:15.483Z", "path": "role/xiongbao/thought/autonovels-creation-thinking.thought.md"}}, {"id": "novel-creation-thinking", "source": "project", "protocol": "thought", "name": "Novel Creation Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/xiongbao/thought/novel-creation-thinking.thought.md", "metadata": {"createdAt": "2025-08-07T14:11:15.483Z", "updatedAt": "2025-08-07T14:11:15.483Z", "scannedAt": "2025-08-07T14:11:15.483Z", "path": "role/xiongbao/thought/novel-creation-thinking.thought.md"}}, {"id": "xiongbao", "source": "project", "protocol": "role", "name": "Xiongbao 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/xiongbao/xiongbao.role.md", "metadata": {"createdAt": "2025-08-07T14:11:15.484Z", "updatedAt": "2025-08-07T14:11:15.484Z", "scannedAt": "2025-08-07T14:11:15.484Z", "path": "role/xiongbao/xiongbao.role.md"}}], "stats": {"totalResources": 29, "byProtocol": {"knowledge": 7, "role": 5, "execution": 11, "thought": 6}, "bySource": {"project": 29}}}