# 🏗️ 大熊工作区结构知识库

## 📂 完整目录结构

```
E:\python\大熊工作区\
├── README.md                    # 工作区说明文档
├── 学习素材\                    # 学习材料存放区
│   ├── [财经大V文章].md        # 优秀文章学习素材
│   └── ...                     # 更多学习材料
└── 创作作品\                    # 创作输出存放区
    ├── article_YYYYMMDD_主题.txt  # 创作的文章
    └── ...                     # 更多创作作品
```

## 🎯 工作区功能定位

### 学习素材区 (`学习素材/`)
- **主要用途**: 存放其他财经类公众号大V的优秀文章
- **文件格式**: 主要为 `.md` 格式
- **命名规范**: `标题.md` 或 `作者_标题.md`
- **学习价值**: 
  - 写作风格参考
  - 行业术语积累
  - 表达技巧学习
  - 文章结构分析

### 创作作品区 (`创作作品/`)
- **主要用途**: 存放大熊创作的所有原创文章
- **文件格式**: `.txt` 和 `.md` 格式
- **命名规范**: `article_YYYYMMDD_主题关键词.txt`
- **管理特点**:
  - 自动时间戳
  - 主题分类
  - 版本管理
  - 质量追踪

## 🔄 工作流程集成

### 学习阶段
1. **素材导入**: 用户将财经文章放入学习素材文件夹
2. **自动扫描**: 大熊定期检查新增学习材料
3. **深度分析**: 分析文章的写作特点和专业内容
4. **知识积累**: 提取有价值的表达方式和行业见解

### 创作阶段
1. **需求分析**: 理解用户的创作要求
2. **素材整合**: 结合学习到的优秀写作风格
3. **内容创作**: 基于专业知识和最新信息创作
4. **静默保存**: 直接保存到创作作品文件夹

## 📊 文件管理规范

### 文件命名约定
- **学习素材**: `[作者]_[标题].md` 或 `[标题].md`
- **创作作品**: `article_[YYYYMMDD]_[主题关键词].txt`
- **特殊标记**: 
  - `_draft` 表示草稿
  - `_final` 表示最终版本
  - `_v2` 表示修订版本

### 文件组织原则
- **时间排序**: 按创作时间组织文件
- **主题分类**: 通过文件名体现主题
- **版本控制**: 保留重要版本的历史记录
- **质量标记**: 标注文章质量等级

## 🎨 创作风格管理

### 风格学习机制
- **多样性**: 从不同作者学习多种写作风格
- **适应性**: 根据主题调整写作风格
- **一致性**: 保持大熊独特的表达特色
- **自然性**: 确保反AI检测的自然表达

### 质量控制标准
- **专业性**: 基于18年投资经验的专业见解
- **时效性**: 结合最新市场信息和政策
- **可读性**: 保持通俗易懂的表达方式
- **独特性**: 形成独特的观点和分析角度

## 🔧 技术实现细节

### 自动化功能
- **文件扫描**: 自动检测新增学习素材
- **内容分析**: 智能分析文章结构和风格
- **保存管理**: 自动命名和路径管理
- **统计报告**: 生成创作统计和质量报告

### 集成工具
- **文件操作**: save-file, view, str-replace-editor
- **内容处理**: 智能文本分析和处理
- **路径管理**: 自动路径解析和文件定位
- **格式转换**: 支持多种文件格式的读写

---
*工作区结构知识 - 支撑大熊高效创作的基础架构* 🏗️
