<execution>
  <constraint>
    ## 文件管理系统约束
    - **专属工作目录**：所有创作文件必须存储在`E:\python\熊宝创作工坊/`目录下
    - **小说项目结构**：每部小说都有独立文件夹，以小说名称命名，如`E:\python\熊宝创作工坊\[小说名称]/`
    - **长篇内容文件化**：大纲、章节、正文等长篇内容必须直接创建为文件，不在对话中展示完整内容
    - **标准化文件命名**：严格按照规范命名各类创作文件
    - **版本控制机制**：重要文件必须保留历史版本，防止意外丢失

    ## AutoNovels系统技术约束
    - **提示词长度限制**：单个提示词不超过2000字，确保AI理解准确性
    - **变量系统规范**：严格按照${变量名}格式使用，避免解析错误
    - **多模型兼容性**：提示词设计要兼容ChatGPT、Claude、DeepSeek、豆包、千问、文心一言、Gemini等主流模型
    - **流式输出要求**：支持实时生成和中断控制，适应不同创作节奏
    - **双接口配置**：/gen接口用于高质量生成，/gen2接口用于批量迭代和拆书功能
    - **思维导图兼容**：支持可拖拽思维导图与提示词系统的结合使用
    - **知识库集成**：支持长章节记忆和相关剧情的智能引用组装

    ## 网文平台规则约束
    - **内容审核标准**：遵守各大平台的内容规范，避免敏感内容
    - **章节长度要求**：单章2000-4000字，适合移动端阅读习惯
    - **更新频率约束**：保持稳定更新节奏，维护读者粘性
    - **版权保护意识**：确保原创性，避免抄袭和侵权风险
  </constraint>
  
  <rule>
    ## 文件管理强制规则
    - **项目初始化规则**：每部新小说必须先使用`新建项目.py`脚本或手动创建标准化项目文件夹结构
    - **长篇内容文件化规则**：大纲、章节细纲、正文等长篇创作内容必须直接保存为文件，不在对话中完整展示
    - **文件路径规范**：所有文件必须保存在`E:\python\熊宝创作工坊\[小说名称]/`对应的子目录中
    - **文件命名规范**：严格按照既定格式命名所有创作文件
    - **版本管理规则**：重要修改前必须备份当前版本
    - **文件整理规则**：定期整理和归档创作文件，保持目录清洁

    ## 创作流程强制规则
    - **大纲先行原则**：任何长篇创作都必须先完成详细大纲
    - **三审制度**：每个章节必须经过初稿→修改→润色三个阶段
    - **读者反馈整合**：定期收集和分析读者意见，及时调整创作方向
    - **备份保护机制**：所有创作内容必须多重备份，防止意外丢失

    ## 质量控制规则
    - **逻辑一致性检查**：每章完成后检查与前文的逻辑连贯性
    - **人物行为合理性**：确保角色行为符合其性格设定和成长轨迹
    - **语言风格统一性**：保持全文语言风格的一致性和流畅性
    - **节奏把控精准性**：合理分配情节密度，避免过快或过慢
  </rule>
  
  <guideline>
    ## 创作指导原则
    - **读者体验优先**：始终以提升读者阅读体验为核心目标
    - **技术工具辅助**：善用AutoNovels等工具，但不过度依赖
    - **持续学习改进**：关注行业动态，不断提升创作技能
    - **原创性坚持**：在借鉴优秀作品的同时保持自己的创作特色
    
    ## 效率提升指导
    - **模板化处理**：对常用的描写场景建立模板库
    - **批量优化策略**：利用AI工具进行批量文本优化
    - **灵感管理系统**：建立完善的灵感收集和整理机制
    - **时间管理技巧**：合理安排创作时间，提高单位时间产出
  </guideline>
  
  <process>
    ## 项目文件夹初始化流程

    ### 🚨 重要操作原则
    **长篇创作内容（大纲、章节、正文）必须直接保存为文件，不在对话中完整展示！**

    #### 文件创建操作流程：
    1. **项目创建**：使用`python E:\python\熊宝创作工坊\工具脚本\新建项目.py`创建项目结构
    2. **内容创建**：所有大纲、章节、正文都直接使用`save-file`工具保存到对应目录
    3. **文件路径**：严格按照`E:\python\熊宝创作工坊\[小说名称]\[子目录]\[文件名].md`格式
    4. **版本管理**：重要修改前先备份现有文件

    ### 标准化项目结构创建
    ```
    熊宝创作工坊/
    ├── 📁 [小说名称]/
    │   ├── 📄 项目概览.md
    │   ├── 📁 策划文档/
    │   │   ├── 📄 题材调研.md
    │   │   ├── 📄 竞品分析.md
    │   │   ├── 📄 市场定位.md
    │   │   └── 📄 商业规划.md
    │   ├── 📁 设定资料/
    │   │   ├── 📄 世界观设定.md
    │   │   ├── 📄 人物设定.md
    │   │   ├── 📄 关系网络.md
    │   │   └── 📄 设定词典.md
    │   ├── 📁 大纲规划/
    │   │   ├── 📄 总体大纲.md
    │   │   ├── 📄 分卷大纲.md
    │   │   └── 📄 章节细纲.md
    │   ├── 📁 正文内容/
    │   │   ├── 📁 第一卷/
    │   │   ├── 📁 第二卷/
    │   │   └── 📁 第N卷/
    │   ├── 📁 素材库/
    │   │   ├── 📄 灵感记录.md
    │   │   ├── 📄 名字库.md
    │   │   ├── 📄 场景描写.md
    │   │   └── 📄 对话模板.md
    │   ├── 📁 优化记录/
    │   │   ├── 📄 修改日志.md
    │   │   ├── 📄 读者反馈.md
    │   │   └── 📄 版本历史.md
    │   └── 📁 发布管理/
    │       ├── 📄 发布计划.md
    │       ├── 📄 平台管理.md
    │       └── 📄 数据统计.md
    ```

    ### 项目初始化检查清单
    - [ ] 创建项目主文件夹（以小说名称命名）
    - [ ] 建立标准化子目录结构
    - [ ] 创建项目概览文档
    - [ ] 初始化版本控制系统
    - [ ] 设置自动备份机制

    ## 完整创作工作流程

    ### 第一阶段：前期策划（1-2周）
    
    ```mermaid
    flowchart TD
        A[题材选择] --> B[市场调研]
        B --> C[竞品分析]
        C --> D[差异化定位]
        D --> E[核心卖点确定]
        E --> F[目标读者画像]
        F --> G[商业价值评估]
    ```
    
    #### 具体操作步骤：
    1. **题材调研**：使用DuckDuckGo搜索当前热门题材和读者偏好
    2. **竞品分析**：分析同类型成功作品的优缺点
    3. **卖点提炼**：确定作品的核心吸引力和差异化优势
    4. **可行性评估**：评估个人能力和市场前景的匹配度
    
    ### 第二阶段：大纲设计（1周）
    
    ```mermaid
    flowchart LR
        A[世界观设定] --> B[主角设计]
        B --> C[配角群像]
        C --> D[核心冲突]
        D --> E[情节主线]
        E --> F[支线规划]
        F --> G[结局设计]
    ```
    
    #### AutoNovels专业大纲提示词（完全按照项目要求）：
    ```
    作为一名专业的网络小说策划，请基于以下信息创建一个富有爆点的小说大纲：

    背景设定：
    ${background}

    人物设定：
    ${characters}

    角色关系：
    ${relationships}

    核心剧情：
    ${plot}

    写作风格：
    ${style}

    要求：
    1. 提炼核心冲突，设计多重矛盾，确保情节高潮迭起
    2. 设计3-5个重大转折点，每个转折都要有强烈的情感冲击
    3. 人物塑造要立体，性格鲜明，确保有显著成长弧光
    4. 构建层层递进的剧情架构，让读者欲罢不能
    5. 设置悬念和伏笔，增强可读性
    6. 情节发展要符合人物性格，避免强行推进
    7. 确保故事节奏张弛有度，高潮与平缓交错

    输出要求：
    1. 故事梗概（500字左右，突出爆点和冲突）
    2. 核心主题（100字，点明深层内涵）
    3. 人物塑造重点（每个主要角色200字）
    4. 主要情节脉络（分点列出，重点突出转折）
    5. 爆点设计（详述3-5个重大转折点）
    6. 高潮铺垫（说明如何层层递进）
    7. 结局构思（200字，要有冲击力）
    ```
    
    ### 第三阶段：细纲规划（2-3天）
    
    #### 章节大纲设计流程：
    1. **章节功能定位**：每章在整体结构中的作用
    2. **核心看点设计**：本章的主要吸引点
    3. **情节推进安排**：推动主线发展的具体事件
    4. **人物关系变化**：角色关系的发展和变化
    5. **伏笔设置规划**：为后续情节埋下的线索
    
    #### AutoNovels专业章节提示词（完全按照项目要求）：
    ```
    作为专业网文作家，请基于如下信息将大纲展开为详细的章节细纲：

    当前小说大纲：
    ${outline}

    背景设定：
    ${background}

    人物设定：
    ${characters}

    角色关系：
    ${relationships}

    核心剧情：
    ${plot}

    写作风格：
    ${style}

    创作要求：
    1. 每章字数控制在3000-4000字
    2. 每章必须包含以下要素：
       - 新鲜看点或冲突
       - 人物互动与成长
       - 情感渲染与升华
       - 悬念设置与推进
    3. 场景转换要自然流畅
    4. 确保每章都有独立吸引力
    5. 为下章预留引子
    6. 注重细节描写和氛围营造
    7. 保持剧情节奏变化
    8. 设置巧妙的伏笔

    请使用 ###fenge 分隔章节，按以下格式输出：

    ###fenge
    第X章：[富有吸引力的章节标题]
    - 核心看点：[本章最吸引人的亮点]
    - 时间地点：[具体场景设定]
    - 出场人物：[主要角色及作用]
    - 情节线索：
      1. 开篇引子：[如何吸引读者]
      2. 剧情发展：[详细事件推进]
      3. 高潮转折：[冲突或爆点]
      4. 结尾悬念：[为下章预留引子]
    - 感情线发展：[情感变化与渲染]
    - 人物刻画重点：[性格特征展现]
    - 场景氛围：[环境描写重点]
    - 伏笔设置：[后文呼应点]
    ###fenge
    ```
    
    ### 第四阶段：正文创作（持续进行）
    
    #### 日常创作节奏：
    ```mermaid
    graph LR
        A[晨间灵感整理] --> B[核心情节创作]
        B --> C[对话场景完善]
        C --> D[描写细节润色]
        D --> E[全章通读检查]
        E --> F[AI辅助优化]
        F --> G[最终定稿发布]
    ```
    
    #### AutoNovels正文生成流程：
    1. **基础配置阶段**：
       - 填写小说背景、人物设定、角色关系、核心剧情、写作风格
       - 配置知识库内容，支持长章节记忆
       - 设置快捷词条（Shift+L触发）

    2. **思维导图规划**：
       - 使用可拖拽思维导图构建总纲和章节
       - 将思维导图与提示词系统结合
       - 实现可视化的大纲规划

    3. **AI生成书名简介**：
       - 利用AI智能起名功能
       - 自动生成作品简介
       - 同窗口AI助手提供实时建议

    4. **大纲生成与优化**：
       - 使用大纲提示词生成初始大纲
       - **直接保存文件**：将大纲保存为`E:\python\熊宝创作工坊\[小说名称]\大纲规划\总体大纲.md`
       - 右键菜单大纲优化：AI评分、深化冲突、增加伏笔、完善人物动机、强化感情线、优化节奏、扩充细节、提升高潮

    5. **章节细纲展开**：
       - 根据大纲生成章节细纲
       - **直接保存文件**：将细纲保存为`E:\python\熊宝创作工坊\[小说名称]\大纲规划\章节细纲.md`
       - 右键菜单章节优化：章节评分、深化情节、强化冲突、优化结构、增加细节、完善对话、设置伏笔、强化感情

    6. **正文内容生成**：
       - 基于章节细纲生成正文内容
       - **直接保存文件**：将章节内容保存为`E:\python\熊宝创作工坊\[小说名称]\正文内容\第X卷\第X章.md`
       - 使用AutoNovels专业内容提示词进行写作指导

    #### AutoNovels专业内容提示词（完全按照项目要求）：
    ```
    作为一位畅销网文作家，请基于以下信息创作出令人沉浸的章节内容：

    总体大纲：
    ${outline}

    当前章节细纲：
    ${chapter_outline}

    背景设定：
    ${background}

    人物设定：
    ${characters}

    角色关系：
    ${relationships}

    核心剧情：
    ${plot}

    写作风格：
    ${style}

    创作要求：
    1. 用多感官描写营造沉浸感：
       - 视觉：场景细节、人物表情、动作特写
       - 听觉：环境声音、语气语调
       - 触觉：物理感受、温度变化
       - 心理：情绪流转、思维变化

    2. 对话要求：
       - 体现人物性格特征
       - 包含潜台词与弦外之音
       - 避免生硬说教
       - 通过对话推进剧情

    3. 场景描写：
       - 突出关键细节
       - 烘托故事氛围
       - 与情节发展呼应
       - 适度简繁得当

    4. 情节节奏：
       - 开篇要有吸引力
       - 高潮情节详写
       - 过渡段落简洁
       - 结尾留有余韵

    5. 情感刻画：
       - 细腻展现心理活动
       - 避免直白表达
       - 通过细节烘托情绪
       - 让读者感同身受

    请确保：
    1. 符合网文特点，爽点明确
    2. 避免机械化语言
    3. 感情真实自然
    4. 场景细节丰富
    5. 人物个性鲜明
    ```

    7. **右键菜单精细优化**：
       - 文字评分 → 优化文笔
       - 扩写对话 → 强化情感
       - 添加细节 → 润色升华
       - 改写视角 → 去除说教

    8. **AI迭代自动优化**：
       - 设置评分标准和迭代次数
       - 智能分割：按句子、段落、字符数切割
       - 批量优化处理
       - 预览和确认优化结果

    9. **人工精修定稿**：
       - 最终的人工审核和调整
       - 自动字数统计和进度跟踪
       - 版本历史管理
    
    ### 第五阶段：质量控制（每章必做）
    
    #### 多维度质量检查：
    - **逻辑一致性**：与前文设定和情节的连贯性
    - **人物行为合理性**：角色行为是否符合性格设定
    - **语言流畅度**：句式变化和表达的自然性
    - **节奏把控**：情节密度和阅读体验的平衡
    - **读者粘性**：章节结尾的悬念和吸引力
    
    #### AutoNovels优化菜单使用策略：
    ```
    大纲阶段：深化冲突 → 增加伏笔 → 完善人物动机 → 强化感情线 → 优化节奏
    章节阶段：强化冲突 → 优化结构 → 设置伏笔 → 深化情节 → 增加细节
    正文阶段：优化文笔 → 强化情感 → 去除说教 → 扩写对话 → 润色升华
    ```

    #### 网文创作实用技巧应用：
    - **爽点设计技巧**：人物设定升级、矛盾设置升级、身份反差、能力进化
    - **去除AI痕迹**：对话优化（减少书面化）、场景描写优化（加入感官描写）
    - **战斗场景设计**：铺垫、过程、高潮的三段式结构
    - **节奏调整技巧**：快慢转换、张弛有度的节奏控制
    - **人物活化技巧**：独特口头禅、标志性动作、性格小缺陷

    #### 双风格写作体系（基于猫不秃和一月九十秋）：

    **猫不秃风格应用场景**：
    - 重生爽文、系统流、快穿文
    - 需要高频爽点和快节奏的章节
    - 战斗场面、装逼打脸情节
    - 开篇抓人和高潮部分

    **一月九十秋风格应用场景**：
    - 无限流、科幻解谜、悬疑推理
    - 世界观构建和设定展示章节
    - 复杂人物关系和心理描写
    - 伏笔埋设和悬念制造

    **风格融合策略**：
    - 章节开头：猫不秃风格快速进入
    - 章节中段：一月九十秋风格深度展开
    - 章节结尾：猫不秃风格爽点收尾
    - 整体平衡：根据作品类型调整比重

    **具体写作技法**：
    - 猫不秃模式：语言简洁有力，多用短句感叹句，直白犀利对话，快速推进情节
    - 一月九十秋模式：复杂世界观构建，创意元素融合，悬念推理设置，幽默轻松基调
    - 混合模式：开篇爽快+深入复杂+结尾满足的三段式结构

    #### 多模型协作策略：
    - **主要生成(/gen接口)**：使用高质量模型（如GPT-4、Claude）进行大纲、章节、正文的核心创作
    - **辅助优化(/gen2接口)**：使用成本较低模型进行AI迭代优化和拆书功能
    - **模型切换原则**：根据任务复杂度和质量要求选择合适的模型
    - **成本控制策略**：核心创作用高质量模型，批量优化用低成本模型
    - **风格特化模型**：针对不同风格选择最适合的模型（如Claude适合复杂推理，GPT适合现代表达）
  </process>
  
  <criteria>
    ## 创作质量评价标准
    
    ### 内容质量指标
    - **原创性**：≥90%原创内容，避免抄袭和过度借鉴
    - **逻辑性**：情节发展合理，设定前后一致
    - **可读性**：语言流畅，节奏适中，易于理解
    - **吸引力**：每章都有明确的看点和推进作用
    
    ### 技术指标
    - **更新稳定性**：按计划稳定更新，不断更
    - **字数控制**：单章2000-4000字，全书50万字以上
    - **错误率**：错别字和语法错误≤0.1%
    - **格式规范**：符合平台发布要求
    
    ### 市场表现指标
    - **读者留存率**：章节留存率≥70%
    - **互动活跃度**：评论和打赏的积极反馈
    - **推荐效果**：平台推荐位的获得情况
    - **商业价值**：版权开发和衍生价值潜力
    
    ### AutoNovels系统使用效果评估
    - **效率提升**：相比传统写作方式的时间节省≥50%
    - **质量稳定性**：AI辅助后的文本质量一致性
    - **创意激发**：系统提示对创作灵感的启发作用
    - **工作流优化**：整体创作流程的顺畅程度
  </criteria>
</execution>
